-- /api3 Unified Database Schema Creation
-- Based on cost-quota-consolidation-strategy.md
-- Database: pictometry_v3 (new database for /api3)

-- 1. Create users table (enhanced from original)
CREATE TABLE users (
    users_id SERIAL PRIMARY KEY,
    users_searches SMALLINT NOT NULL,           -- Allocated quota amount
    users_ip VARCHAR(45) NOT NULL,              -- IP address
    users_timestamp TIMESTAMPTZ NOT NULL DEFAULT now(),
    users_accesscode VARCHAR(255) NOT NULL UNIQUE  -- Primary identifier for users
    -- Note: users_forcedownload removed - handled by access_bypasses table
);

-- 2. Create imagery_purchases table (replaces: tiles + eagleview_purchases)
CREATE TABLE imagery_purchases (
    id SERIAL PRIMARY KEY,
    accesscode TEXT NOT NULL,
    provider TEXT NOT NULL,                    -- 'nearmap', 'eagleview', 'pictometry'
    layer_id TEXT,                            -- EagleView layer ID, NULL for others
    bounds GEOMETRY(POLYGON, 4326) NOT NULL,
    tile_matrix_set TEXT,                     -- EagleView specific
    imagery_date DATE,
    purchase_date TIMESTAMP DEFAULT NOW(),
    cost NUMERIC(10,2) NOT NULL,
    storage_url TEXT,                         -- For cached tiles
    status TEXT DEFAULT 'active'              -- 'active', 'expired', 'cached'
);

-- 3. Create usage_logs table (replaces: logs + eagleview_access_log + nearmap_logs)
CREATE TABLE usage_logs (
    id SERIAL PRIMARY KEY,
    accesscode TEXT NOT NULL,
    provider TEXT NOT NULL,
    purchase_id INTEGER REFERENCES imagery_purchases(id),
    access_type TEXT NOT NULL,                -- 'new_purchase', 'cached_access', 'free_reuse'
    cost_charged NUMERIC(10,2) DEFAULT 0,
    building_id INTEGER,
    map_id INTEGER,
    tile_coordinates JSONB,                   -- {z, x, y} for tile access
    access_date TIMESTAMP DEFAULT NOW(),
    
    -- Additional fields for detailed logging
    uncompressed_size INTEGER,                -- For Nearmap compatibility
    area_acres NUMERIC(10,4)                 -- Area in acres
);

-- 4. Create access_bypasses table (replaces: nearmap_accesscode_bypass + user.forcedownload)
CREATE TABLE access_bypasses (
    id SERIAL PRIMARY KEY,
    accesscode TEXT NOT NULL UNIQUE,
    bypass_type TEXT NOT NULL,                -- 'worker', 'admin', 'free_tier'
    created_date TIMESTAMP DEFAULT NOW(),
    expires_date TIMESTAMP                    -- NULL for permanent bypass
);

-- Create indexes for performance
CREATE INDEX idx_imagery_purchases_accesscode ON imagery_purchases(accesscode);
CREATE INDEX idx_imagery_purchases_provider ON imagery_purchases(provider);
CREATE INDEX idx_imagery_purchases_bounds ON imagery_purchases USING GIST(bounds);
CREATE INDEX idx_imagery_purchases_status ON imagery_purchases(status);

CREATE INDEX idx_usage_logs_accesscode ON usage_logs(accesscode);
CREATE INDEX idx_usage_logs_provider ON usage_logs(provider);
CREATE INDEX idx_usage_logs_access_date ON usage_logs(access_date);
CREATE INDEX idx_usage_logs_purchase_id ON usage_logs(purchase_id);

CREATE INDEX idx_access_bypasses_accesscode ON access_bypasses(accesscode);
CREATE INDEX idx_access_bypasses_type ON access_bypasses(bypass_type);

-- Grant permissions to pictometry user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO pictometry;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO pictometry;
