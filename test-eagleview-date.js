// Test script to verify EagleView date handling
const fetch = require('node-fetch');

async function testEagleViewDatePassing() {
    console.log('Testing EagleView date parameter passing...');
    
    const testData = {
        accesscode: '8f85517967795eeef66c225f7883bdcb',
        layer: 'test-layer-2023-08-15',
        bounds: [[-77.539, 40.005], [-77.524, 40.005], [-77.524, 40.013], [-77.539, 40.013], [-77.539, 40.005]],
        tileMatrixSet: 'GoogleMapsCompatible_9-23',
        imageryDate: '2023-08-15'
    };
    
    try {
        const response = await fetch('https://tiles.sitefotos.com/api3/tiles/eagleview/purchase', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testData)
        });
        
        console.log('Response status:', response.status);
        const responseText = await response.text();
        console.log('Response body:', responseText);
        
        // We expect this to fail (since it's a test layer), but we want to see if the date parameter is being processed
        
    } catch (error) {
        console.error('Request error:', error.message);
    }
}

testEagleViewDatePassing();
