-- Data Migration Script: pictometry -> pictometry_v3
-- Based on cost-quota-consolidation-strategy.md
-- This script migrates data from the old /api2 database to the new /api3 unified schema

-- Connect to the new database
\c pictometry_v3

-- 1. Migrate users table data (sum allocations per access code)
INSERT INTO users (users_searches, users_ip, users_timestamp, users_accesscode)
SELECT
    SUM(users_searches) as users_searches,
    MAX(users_ip) as users_ip,
    MAX(users_timestamp) as users_timestamp,
    users_accesscode
FROM dblink('host=127.0.0.1 port=5432 dbname=pictometry user=pictometry password=Y^<Ta5C+H(d|$,Gj',
    'SELECT users_searches, users_ip, users_timestamp, users_accesscode FROM postgis.users')
AS old_users(users_searches SMALLINT, users_ip VARCHAR(45), users_timestamp TIMESTAMPTZ, users_accesscode VARCHAR(255))
GROUP BY users_accesscode
ON CONFLICT (users_accesscode) DO UPDATE SET
    users_searches = EXCLUDED.users_searches;

-- 2. Migrate tiles table data to imagery_purchases
INSERT INTO imagery_purchases (
    accesscode, provider, bounds, imagery_date, cost, storage_url, status, purchase_date
)
SELECT
    split_part(tiles_accesscode, ',', 1) as accesscode,
    COALESCE(tiles_provider, 'nearmap') as provider,
    tiles_bound as bounds,
    tiles_imagery_date as imagery_date,
    COALESCE(tiles_cost, 0) as cost,
    COALESCE(tiles_url, tiles_path) as storage_url, -- Use tiles_url first, fallback to tiles_path
    'cached' as status,
    COALESCE(tiles_timestamp, NOW()) as purchase_date
FROM dblink('host=127.0.0.1 port=5432 dbname=pictometry user=pictometry password=Y^<Ta5C+H(d|$,Gj',
    'SELECT tiles_accesscode, tiles_provider, tiles_bound, tiles_imagery_date, tiles_cost, tiles_path, tiles_url,
     NOW() as tiles_timestamp FROM postgis.tiles WHERE tiles_provider IS NOT NULL')
AS old_tiles(tiles_accesscode TEXT, tiles_provider VARCHAR(20), tiles_bound GEOMETRY,
             tiles_imagery_date DATE, tiles_cost REAL, tiles_path VARCHAR(255), tiles_url VARCHAR(255), tiles_timestamp TIMESTAMPTZ);

-- 3. Migrate eagleview_purchases data to imagery_purchases
INSERT INTO imagery_purchases (
    accesscode, provider, layer_id, bounds, tile_matrix_set, cost, status, purchase_date
)
SELECT
    accesscode,
    'eagleview' as provider,
    layer as layer_id,
    bounds,
    tile_matrix_set,
    0 as cost, -- EagleView purchases weren't charged yet
    'active' as status,
    created_at as purchase_date
FROM dblink('host=127.0.0.1 port=5432 dbname=pictometry user=pictometry password=Y^<Ta5C+H(d|$,Gj',
    'SELECT accesscode, layer, bounds, tile_matrix_set, created_at FROM public.eagleview_purchases')
AS old_eagleview(accesscode VARCHAR(255), layer VARCHAR(255), bounds GEOMETRY, 
                 tile_matrix_set VARCHAR(255), created_at TIMESTAMPTZ);

-- 4. Migrate logs data to usage_logs
INSERT INTO usage_logs (
    accesscode, provider, purchase_id, access_type, cost_charged,
    building_id, map_id, access_date
)
SELECT
    l.logs_accesscode,
    COALESCE(t.tiles_provider, 'unknown') as provider,
    ip.id as purchase_id,
    CASE l.logs_access_type
        WHEN 0 THEN 'self_cached'      -- User's own cached tile (FREE)
        WHEN 1 THEN 'new_purchase'     -- New tile purchase (CHARGED)
        WHEN 2 THEN 'shared_cached'    -- Shared cached tile (CHARGED)
        WHEN 3 THEN 'click_access'     -- Click access (FREE)
        ELSE 'unknown'
    END as access_type,
    COALESCE(l.logs_cost, 0) as cost_charged,
    l.logs_bid,
    l.logs_mapid,
    l.logs_timestamp as access_date
FROM dblink('host=127.0.0.1 port=5432 dbname=pictometry user=pictometry password=Y^<Ta5C+H(d|$,Gj',
    'SELECT logs_accesscode, logs_access_type, logs_cost, logs_bid, logs_mapid, logs_timestamp,
     logs_tiles_id FROM postgis.logs')
AS l(logs_accesscode VARCHAR(255), logs_access_type SMALLINT, logs_cost DOUBLE PRECISION, 
     logs_bid INTEGER, logs_mapid INTEGER, logs_timestamp TIMESTAMPTZ, logs_tiles_id BIGINT)
LEFT JOIN dblink('host=127.0.0.1 port=5432 dbname=pictometry user=pictometry password=Y^<Ta5C+H(d|$,Gj',
    'SELECT tiles_id, tiles_provider, tiles_path, tiles_url FROM postgis.tiles')
AS t(tiles_id BIGINT, tiles_provider VARCHAR(20), tiles_path VARCHAR(255), tiles_url VARCHAR(255)) ON l.logs_tiles_id = t.tiles_id
LEFT JOIN imagery_purchases ip ON ip.storage_url = COALESCE(t.tiles_url, t.tiles_path) AND ip.accesscode = l.logs_accesscode;

-- 5. Migrate bypass data to access_bypasses
INSERT INTO access_bypasses (accesscode, bypass_type)
SELECT accesscode, 'worker' as bypass_type
FROM dblink('host=127.0.0.1 port=5432 dbname=pictometry user=pictometry password=Y^<Ta5C+H(d|$,Gj',
    'SELECT accesscode FROM postgis.nearmap_accesscode_bypass')
AS old_bypass(accesscode VARCHAR(255))
ON CONFLICT (accesscode) DO NOTHING;

-- 6. Migrate users with forcedownload to access_bypasses
INSERT INTO access_bypasses (accesscode, bypass_type)
SELECT users_accesscode, 'admin' as bypass_type
FROM dblink('host=127.0.0.1 port=5432 dbname=pictometry user=pictometry password=Y^<Ta5C+H(d|$,Gj',
    'SELECT users_accesscode FROM postgis.users WHERE users_forcedownload = 1')
AS old_force_users(users_accesscode VARCHAR(255))
ON CONFLICT (accesscode) DO NOTHING;
