const express = require('express')
const generalrouter = require('./generalrouter')
const tilesrouter = require('./tilesrouter')
const nearmaptest = require('./nearmaptest')
const app = express()
const port = 3002
const ip = '127.0.0.1'

app.use(express.json({
  limit: '50mb',
  type: ['application/json', 'text/plain']
}))
app.use(express.urlencoded({
  limit: '50mb',
  extended: true
}))


// New /api3 routes with unified cost and quota management
app.use('/api3/ntest', nearmaptest)
app.use('/api3/general', generalrouter)
app.use('/api3/tiles', tilesrouter)
app.get('/api3', (request, response) => {
  response.json({
    Version: '3',
    Features: [
      'Unified cost and quota management',
      'Enhanced EagleView quota enforcement',
      'Improved error handling with HTTP 402 responses',
      'Real-time quota updates',
      'Consistent cost calculation across all providers'
    ]
  })
})



app.listen(port, ip, () => {
  console.log(`App running on port ${port}.`)
})
