const fetch = require('node-fetch');
const { XMLParser } = require('fast-xml-parser');
const db = require('./db');
const turf = require('@turf/turf')
const apiKey = process.env.EAGLEVIEW_API_KEY || '8329d048-70f6-4bf3-8619-62ef642970cd'; // It's better to use environment variables for API keys.
const capabilitiesUrl = 'https://apis.eagleview.com/imagery/wmts/v1/visual/capabilities.xml';
const tileUrl = 'https://apis.eagleview.com/imagery/wmts/v1/visual/tile';
const PRICING_FACTOR = 15000;
const SQM_TO_ACRES = 0.000247105;
/**
 * Fetches and parses the WMTS GetCapabilities response from EagleView.
 * @param {string} bounds - The bounding box to query for.
 * @returns {Promise<object>} - A promise that resolves to the parsed capabilities.
 */
const getCapabilities = async (boundsPoly) => {
    const url = new URL(capabilitiesUrl);
    url.searchParams.append('api_key', apiKey);
    if (boundsPoly && boundsPoly.geometry && boundsPoly.geometry.coordinates) {
        // Extract bounding box from GeoJSON polygon
        const coordinates = boundsPoly.geometry.coordinates[0];
        let minLat = Infinity, maxLat = -Infinity;
        let minLng = Infinity, maxLng = -Infinity;
        
        for (const [lng, lat] of coordinates) {
            minLat = Math.min(minLat, lat);
            maxLat = Math.max(maxLat, lat);
            minLng = Math.min(minLng, lng);
            maxLng = Math.max(maxLng, lng);
        }
        
        url.searchParams.append('min_longitude', minLng);
        url.searchParams.append('min_latitude', minLat);
        url.searchParams.append('max_longitude', maxLng);
        url.searchParams.append('max_latitude', maxLat);
    }

    const response = await fetch(url.toString(), {
        headers: {
            'Referer': 'https://www.sitefotos.com',
        },
    });
    if (!response.ok) {
        throw new Error(`Failed to fetch capabilities: ${response.statusText}`);
    }

    const xmlData = await response.text();
    const parser = new XMLParser({
        removeNSPrefix: true,
        ignoreAttributes: false,
        parseAttributeValue: true,
    });
    const jsonData = parser.parse(xmlData);

    if (jsonData && jsonData.Capabilities && jsonData.Capabilities.Contents) {
        const tileMatrixSets = {};
        if (jsonData.Capabilities.Contents.TileMatrixSet) {
            const sets = Array.isArray(jsonData.Capabilities.Contents.TileMatrixSet)
                ? jsonData.Capabilities.Contents.TileMatrixSet
                : [jsonData.Capabilities.Contents.TileMatrixSet];
            for (const set of sets) {
                if (set.TileMatrix) {
                    const zoomLevels = set.TileMatrix.map(tm => parseInt(tm.Identifier, 10));
                    tileMatrixSets[set.Identifier] = {
                        minZoom: Math.min(...zoomLevels),
                        maxZoom: Math.max(...zoomLevels),
                    };
                }
            }
        }

        const layers = Array.isArray(jsonData.Capabilities.Contents.Layer) ? jsonData.Capabilities.Contents.Layer : [jsonData.Capabilities.Contents.Layer];
        
        const formattedLayers = layers
            .filter(layer => layer && layer.Identifier !== 'Latest' && layer.Identifier !== 'LatestBestResolution')
            .map(layer => {
                if (!layer) return null;

                const layerTileMatrixSetLinks = layer.TileMatrixSetLink
                    ? (Array.isArray(layer.TileMatrixSetLink) ? layer.TileMatrixSetLink : [layer.TileMatrixSetLink])
                    : [];

                const tmsLink = layerTileMatrixSetLinks.find(link => link && tileMatrixSets[link.TileMatrixSet]);

                if (!tmsLink) return null;

                const zoomInfo = tileMatrixSets[tmsLink.TileMatrixSet];
                if (!zoomInfo || zoomInfo.maxZoom < 21) {
                    return null;
                }

                let datePart;
                if (layer.Abstract) {
                    const abstractText = layer.Abstract;
                    const dateMatch = abstractText.match(/Capture Dates?: ([\d-]+)/);
                    if (dateMatch) {
                        datePart = dateMatch[1];
                    }
                }
                if (!datePart && layer.Title) {
                    // Fallback to title only if Abstract fails
                    const titleMatch = layer.Title.match(/^(\d{4}-\d{2}-\d{2}):/);
                    if (titleMatch) {
                        datePart = titleMatch[1];
                    }
                }


                if (datePart) {
                    const lowerCorner = layer.WGS84BoundingBox.LowerCorner.split(' ');
                    const upperCorner = layer.WGS84BoundingBox.UpperCorner.split(' ');
                    const bounds = [
                        [parseFloat(lowerCorner[1]), parseFloat(lowerCorner[0])],
                        [parseFloat(upperCorner[1]), parseFloat(upperCorner[0])]
                    ];

                    // CRITICAL FIX: Use the user's requested bounds for cost calculation, not the layer's full coverage
                    // The layer's WGS84BoundingBox represents the entire coverage area, which inflates costs
                    const boundsArea = turf.area(boundsPoly); // boundsPoly is the user's requested area
                    const dataEstimate = boundsArea / PRICING_FACTOR;
                    const areaInAcres = boundsArea * SQM_TO_ACRES;
                    let dollarEstimate = Math.ceil((areaInAcres * 0.25) * 2) / 2;
                    return {
                        provider: 'eagleview',
                        id: layer.Identifier,
                        date: datePart,
                        resolution: 'Unknown',
                        isContained: true,
                        cost: dollarEstimate,
                        bounds: bounds,
                        minZoom: zoomInfo.minZoom,
                        maxZoom: zoomInfo.maxZoom,
                        tileMatrixSet: tmsLink.TileMatrixSet,
                    };
                }
                return null;
            })
            .filter(Boolean);

        if (formattedLayers.length > 0) {
            // Sort by date to find the best (most recent) layer that supports high zoom
            formattedLayers.sort((a, b) => new Date(b.date) - new Date(a.date));
            return [formattedLayers[0]]; // Return only the best layer
        }

        return [];
    }

    return [];
};

/**
 * Proxies a tile request to the EagleView WMTS service.
 * @param {object} params - The parameters for the tile request {layer, z, x, y, format}.
 * @returns {Promise<object>} - A promise that resolves to the tile image response.
 */
const getTile = async ({ layer, z, x, y, format, accesscode, tileMatrixSet }) => {
    const tms = tileMatrixSet || 'GoogleMapsCompatible_9-23';
    const url = `${tileUrl}/${layer}/default/${tms}/${z}/${x}/${y}.${format}`;
    
    const response = await fetch(url, {
        headers: {
            'X-API-KEY': apiKey,
            'Referer': 'https://www.sitefotos.com',
        },
    });

    if (!response.ok) {
        throw new Error(`Failed to fetch tile: ${response.statusText}`);
    }

    

    return response.body;
};

module.exports = {
    getCapabilities,
    getTile,
};