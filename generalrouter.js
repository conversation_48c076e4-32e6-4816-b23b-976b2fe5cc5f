const express = require('express')
const router = express.Router()
const db = require('./db')


router.get('/', function (req, res) {
    res.send('Not applicable')
})

router.get('/iscustomer/:accessCode', function (req, res) {
    db.isCustomer(req.params.accessCode).then(function (result) {
        
        if(result.length>0)
            res.send({user: true})
        else
            res.send({user: false})
    }).catch(function (e) {
        res.send(e)
    })
})

router.get('/getquota', async function (req, res) {
    try {
        // Use new unified quota system for /api3
        const quota = await db.getUnifiedQuota(req.query.accesscode);
        res.send({
            allocation: quota.allocation,
            usage: quota.usage,
            remaining: quota.remaining
        });
    } catch (error) {
        console.error('Error in /getquota endpoint:', error);
        res.status(500).send({
            error: 'Failed to get quota information',
            allocation: 0,
            usage: 0,
            remaining: 0
        });
    }
})

router.post('/copytile', function (req,res) {
    if (req.body.password && req.body.accesscode && req.body.url) {
        if (req.body.password == 'Muj56f44f9') {
            db.copyTile(req.body.accesscode, req.body.url).then(function (result) {
                res.send({ success: true })
            }).catch(function (e) {
                res.send(e)
            })
        }
        else {
            res.send({ success: false })
        }
    }
    else {
        res.send({ success: false })
    }

})

router.get('/addquota', function (req, res) {
    var ip = (req.headers['x-forwarded-for'] || req.connection.remoteAddress || '').split(',')[0].trim();
    db.addQuota(req.query.accesscode, req.query.password, req.query.amount, req.query.force, ip).then(function (result) {
        res.send(result)
    }).catch(function (e) {
        res.send(e)
    })
})

router.get('/addbypass', function (req, res) {
    db.addbypass(req.query.accesscode, req.query.password).then(function (result) {
        res.send(result)
    }).catch(function (e) {
        res.send(e)
    })
})




module.exports = router